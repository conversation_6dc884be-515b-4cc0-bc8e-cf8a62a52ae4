#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
场内基金数据技术分析工具
使用Pyecharts绘制交互式图表,显示EMA和MACD等技术指标

依赖包安装:
pip install pyecharts pandas numpy scipy tushare MyTT

注意: 此版本使用Pyecharts替代了matplotlib,提供了更好的交互体验
"""

# 场内基金技术分析工具
# 提供数据获取、缓存、技术指标计算和可视化功能

import pandas as pd
import numpy as np
from MyTT import EMA  # 引入 MyTT 库中 EMA 指标计算函数
import tushare as ts
from datetime import datetime
from pathlib import Path
import configparser
import pyecharts.options as opts
from pyecharts.charts import Line, Grid, Scatter
import sqlite3
import json
from scipy.signal import argrelextrema
import warnings

# 抑制sklearn的警告
warnings.filterwarnings("ignore", category=UserWarning)


class StockDataManager:
    """处理基金数据获取和缓存的类"""

    def __init__(self, api_key=None):
        """初始化StockDataManager

        Args:
            api_key: Tushare API密钥,如果为None则尝试从配置文件加载
        """
        self.api_key = api_key or self._load_api_key()
        if not self.api_key:
            raise ValueError("未提供API密钥,请在配置文件中设置或作为参数传入")
        script_dir = Path(__file__).parent.resolve()
        self.cache_dir = script_dir / "cache"
        self.cache_dir.mkdir(exist_ok=True)
        self.db_path = self.cache_dir / "stock_data_cache.db"
        self._init_db()
        self.today_str = datetime.now().strftime("%Y%m%d")  # 计算一次今天的日期字符串

    def _init_db(self):
        try:
            # 检查数据库文件是否被锁定
            if self.db_path.exists():
                # 尝试以读写模式打开
                self.conn = sqlite3.connect(self.db_path, timeout=10.0)
            else:
                self.conn = sqlite3.connect(self.db_path)

            self.conn.execute(
                """CREATE TABLE IF NOT EXISTS fund_cache (
                ts_code TEXT NOT NULL,
                timestamp TEXT NOT NULL,
                data TEXT NOT NULL,
                PRIMARY KEY (ts_code, timestamp)
            )"""
            )
            self.conn.commit()
        except sqlite3.OperationalError as e:
            print(f"数据库初始化警告: {e}")
            # 如果数据库被锁定,创建一个新的数据库文件
            backup_db_path = self.cache_dir / f"stock_data_cache_backup_{self.today_str}.db"
            print(f"尝试使用备用数据库: {backup_db_path}")
            self.db_path = backup_db_path
            self.conn = sqlite3.connect(self.db_path)
            self.conn.execute(
                """CREATE TABLE IF NOT EXISTS fund_cache (
                ts_code TEXT NOT NULL,
                timestamp TEXT NOT NULL,
                data TEXT NOT NULL,
                PRIMARY KEY (ts_code, timestamp)
            )"""
            )
            self.conn.commit()

    def _load_api_key(self):
        """从配置文件加载API密钥"""
        config = configparser.ConfigParser()
        config_path = Path("config.ini")

        # 默认API密钥,仅作为示例
        default_key = "5aba669a485730beded0d604bd8ccfdaca39f8c9c09ef2d0ed78ed77"

        if config_path.exists():
            try:
                config.read(config_path)
                return config.get("API", "tushare_token", fallback=default_key)
            except Exception as e:
                print(f"读取配置文件失败: {str(e)}")
                return default_key
        else:
            # 创建默认配置文件
            config["API"] = {"tushare_token": default_key}
            with open(config_path, "w") as f:
                config.write(f)
            print(f"已创建默认配置文件: {config_path}")
            return default_key

    def get_cache_key(self, ts_code):
        return ts_code, self.today_str

    def is_cache_valid(self, ts_code):
        ts_code, today_str = self.get_cache_key(ts_code)
        cursor = self.conn.execute(
            "SELECT 1 FROM fund_cache WHERE ts_code=? AND timestamp=?",
            (ts_code, today_str),
        )
        return cursor.fetchone() is not None

    def read_cache(self, ts_code):
        ts_code, today_str = self.get_cache_key(ts_code)
        cursor = self.conn.execute(
            "SELECT data FROM fund_cache WHERE ts_code=? AND timestamp=?",
            (ts_code, today_str),
        )
        row = cursor.fetchone()
        if row:
            return json.loads(row[0])
        return None

    def write_cache(self, ts_code, cache_data):
        try:
            ts_code, today_str = self.get_cache_key(ts_code)
            self.conn.execute(
                "REPLACE INTO fund_cache (ts_code, timestamp, data) VALUES (?, ?, ?)",
                (ts_code, today_str, json.dumps(cache_data)),
            )
            self.conn.commit()
        except sqlite3.OperationalError as e:
            print(f"写入缓存失败: {e}")
            # 缓存写入失败不影响主要功能,只是性能会差一些

    def get_fund_data(
        self, ts_code, start_date=None, end_date=None, force_refresh=False
    ):
        today_str = datetime.now().strftime("%Y%m%d")
        if not force_refresh and self.is_cache_valid(ts_code):
            try:
                cache_data = self.read_cache(ts_code)
                if cache_data is not None:
                    df = pd.DataFrame(cache_data["data"])
                    print(f"使用缓存数据 ({ts_code})")
                    return df
                else:
                    raise ValueError("缓存数据不存在")
            except Exception as e:
                print(f"读取SQLite缓存失败: {str(e)}")
        try:
            pro = ts.pro_api(self.api_key)
            today = datetime.now().strftime("%Y%m%d")
            params = {
                "ts_code": ts_code,
                "start_date": start_date if start_date else "20240101",
                "end_date": end_date if end_date else today,
            }
            df = pro.fund_daily(
                **params, fields="ts_code,trade_date,open,close,high,low,vol,amount"
            )
            if not df.empty:
                df = df.rename(columns={"vol": "volume"})
                df = df.sort_values("trade_date", ascending=True).reset_index(drop=True)
                if end_date:
                    df = df[df["trade_date"] <= end_date]
                # type: ignore
                records_dict = df.to_dict("records")  # type: ignore
                cache_data = {"timestamp": today_str, "data": records_dict}
                self.write_cache(ts_code, cache_data)
                print(f"已更新SQLite缓存数据 ({ts_code})")
                return df
            else:
                print(f"未找到基金 {ts_code} 在指定日期区间的数据")
                return pd.DataFrame()
        except Exception as e:
            print(f"获取基金数据失败: {str(e)}")
            return pd.DataFrame()

    def get_available_funds(self):
        """获取常用的场内基金列表"""
        return {
            "501312.SH": "海外科技LOF",
            "513260.SH": "恒生科技ETF",
            "513060.SH": "恒生医疗",
            "159302.SZ": "港股高股息ETF",
            "512890.SH": "红利低波ETF",
            "513630.SH": "港股红利指数ETF",
            "515100.SH": "红利低波100ETF",
            "517900.SH": "银行ETF优选",
            "160723.SZ": "嘉实原油LOF",
            "159509.SZ": "纳指科技ETF",
            "515300.SH": "日经225ETF",
            "159985.SZ": "豆粕ETF",
        }


class TechnicalIndicator:
    """计算技术指标的类"""

    @staticmethod
    def calculate_ema(data, long_term=26, short_term=13):
        """计算长短期EMA及其差值

        Args:
            data: 收盘价数据数组
            long_term: 长期EMA周期数
            short_term: 短期EMA周期数

        Returns:
            tuple: (长期EMA, 短期EMA, 差值)
        """
        ema_long = EMA(data, long_term)
        ema_short = EMA(data, short_term)

        # 显式转换为numpy array以消除类型检查警告
        ema_difference = np.array(ema_short) - np.array(ema_long)

        return ema_long, ema_short, ema_difference

    @staticmethod
    def calculate_macd(data, fast=12, slow=26, signal=9):
        """计算MACD指标

        Args:
            data: 收盘价数据
            fast: 快线周期
            slow: 慢线周期
            signal: 信号线周期

        Returns:
            tuple: (DIF线, DEA信号线, MACD柱状图)
        """
        # 确保数据是numpy数组类型
        data = np.array(data)

        # 计算快线和慢线EMA
        ema_fast = EMA(data, fast)
        ema_slow = EMA(data, slow)

        # 计算DIF
        dif = np.array(ema_fast) - np.array(ema_slow)

        # 计算DEA信号线
        dea = EMA(dif, signal)

        # 计算MACD柱状图
        macd = (np.array(dif) - np.array(dea)) * 2

        return dif, dea, macd

    @staticmethod
    def smooth_data(data, window_size=None):
        """使用EWMA(指数加权移动平均)平滑处理数据

        Args:
            data: 需要平滑的数据数组
            window_size: 窗口大小,如果为None则自动计算

        Returns:
            平滑后的数据
        """
        data_len = len(data)

        # 如果数据长度不足,返回原始数据
        if data_len < 5:
            return data

        # 自动计算窗口大小(如果未指定)
        if window_size is None:
            window_size = min(int(data_len * 0.15), 51)
            # 确保窗口大小为奇数
            if window_size % 2 == 0:
                window_size += 1

        # 确保窗口大小有效
        window_size = max(3, window_size)

        # 指数加权移动平均
        alpha = 2 / (window_size + 1)  # 平滑因子
        return pd.Series(data).ewm(alpha=alpha, adjust=False).mean().values

    @staticmethod
    def calculate_fibonacci_levels(high, low):
        """计算斐波那契回调和延伸水平

        Args:
            high: 高点价格
            low: 低点价格

        Returns:
            dict: 斐波那契回调和延伸水平
        """
        diff = high - low

        # 常用斐波那契回调比例: 0.236, 0.382, 0.5, 0.618, 0.786
        # 常用斐波那契延伸比例: 1.272, 1.618, 2.618, 4.236
        levels = {
            "回调_0.236": high - 0.236 * diff,
            "回调_0.382": high - 0.382 * diff,
            "回调_0.5": high - 0.5 * diff,
            "回调_0.618": high - 0.618 * diff,
            "回调_0.786": high - 0.786 * diff,
            "延伸_1.272": low + 1.272 * diff,
            "延伸_1.618": low + 1.618 * diff,
            "延伸_2.618": low + 2.618 * diff,
            "延伸_4.236": low + 4.236 * diff,
        }

        return levels

    @staticmethod
    def detect_head_shoulders(prices, window_size=20, height_threshold=0.02):
        """检测头肩顶/底形态

        Args:
            prices: 价格序列
            window_size: 用于寻找峰值的窗口大小
            height_threshold: 高度阈值,头部与肩部高度差的最小比例

        Returns:
            tuple: (头肩顶位置列表, 头肩底位置列表)
        """
        # 确保有足够的数据点
        if len(prices) < window_size * 3:
            return [], []

        # 寻找局部极大值点和极小值点
        maxima = argrelextrema(np.array(prices), np.greater, order=window_size)[0]
        minima = argrelextrema(np.array(prices), np.less, order=window_size)[0]

        if len(maxima) < 3 or len(minima) < 2:
            return [], []

        # 头肩顶形态检测 (三个峰,中间峰最高)
        head_shoulders_top = []
        for i in range(len(maxima) - 2):
            left_shoulder = maxima[i]
            head = maxima[i + 1]
            right_shoulder = maxima[i + 2]

            # 检查峰值顺序
            if not (left_shoulder < head < right_shoulder):
                continue

            # 检查头部必须高于两肩
            if not (
                prices[head] > prices[left_shoulder]
                and prices[head] > prices[right_shoulder]
            ):
                continue

            # 检查两肩高度相近 (相差不超过20%)
            shoulder_diff = (
                abs(prices[left_shoulder] - prices[right_shoulder])
                / prices[left_shoulder]
            )
            if shoulder_diff > 0.2:
                continue

            # 检查头部比肩部高出足够量
            height_diff_left = (prices[head] - prices[left_shoulder]) / prices[
                left_shoulder
            ]
            height_diff_right = (prices[head] - prices[right_shoulder]) / prices[
                right_shoulder
            ]
            if (
                height_diff_left < height_threshold
                or height_diff_right < height_threshold
            ):
                continue

            # 寻找颈线 (两肩之间的低点)
            neck_candidates = [
                idx for idx in minima if left_shoulder < idx < right_shoulder
            ]
            if not neck_candidates:
                continue

            neck = neck_candidates[np.argmin([prices[idx] for idx in neck_candidates])]

            # 检查右肩后是否有低于颈线的点 (形成突破)
            breakthrough = False
            for j in range(
                right_shoulder + 1, min(right_shoulder + window_size, len(prices))
            ):
                if j < len(prices) and prices[j] < prices[neck]:
                    breakthrough = True
                    break

            if breakthrough:
                head_shoulders_top.append((left_shoulder, head, right_shoulder, neck))

        # 头肩底形态检测 (三个谷,中间谷最低)
        head_shoulders_bottom = []
        for i in range(len(minima) - 2):
            left_shoulder = minima[i]
            head = minima[i + 1]
            right_shoulder = minima[i + 2]

            # 检查谷值顺序
            if not (left_shoulder < head < right_shoulder):
                continue

            # 检查头部必须低于两肩
            if not (
                prices[head] < prices[left_shoulder]
                and prices[head] < prices[right_shoulder]
            ):
                continue

            # 检查两肩高度相近 (相差不超过20%)
            shoulder_diff = (
                abs(prices[left_shoulder] - prices[right_shoulder])
                / prices[left_shoulder]
            )
            if shoulder_diff > 0.2:
                continue

            # 检查头部比肩部低出足够量
            height_diff_left = (prices[left_shoulder] - prices[head]) / prices[head]
            height_diff_right = (prices[right_shoulder] - prices[head]) / prices[head]
            if (
                height_diff_left < height_threshold
                or height_diff_right < height_threshold
            ):
                continue

            # 寻找颈线 (两肩之间的高点)
            neck_candidates = [
                idx for idx in maxima if left_shoulder < idx < right_shoulder
            ]
            if not neck_candidates:
                continue

            neck = neck_candidates[np.argmax([prices[idx] for idx in neck_candidates])]

            # 检查右肩后是否有高于颈线的点 (形成突破)
            breakthrough = False
            for j in range(
                right_shoulder + 1, min(right_shoulder + window_size, len(prices))
            ):
                if j < len(prices) and prices[j] > prices[neck]:
                    breakthrough = True
                    break

            if breakthrough:
                head_shoulders_bottom.append(
                    (left_shoulder, head, right_shoulder, neck)
                )

        return head_shoulders_top, head_shoulders_bottom

    @staticmethod
    def detect_double_top_bottom(
        prices, window_size=20, height_threshold=0.02, similarity_threshold=0.03
    ):
        """检测双顶/双底形态

        Args:
            prices: 价格序列
            window_size: 用于寻找峰值的窗口大小
            height_threshold: 高度阈值,顶部或底部与中间点高度差的最小比例
            similarity_threshold: 两个顶/底相似度阈值

        Returns:
            tuple: (双顶位置列表, 双底位置列表)
        """
        # 确保有足够的数据点
        if len(prices) < window_size * 2:
            return [], []

        # 寻找局部极大值点和极小值点
        maxima = argrelextrema(np.array(prices), np.greater, order=window_size)[0]
        minima = argrelextrema(np.array(prices), np.less, order=window_size)[0]

        if len(maxima) < 2 or len(minima) < 1:
            return [], []

        # 双顶形态检测
        double_tops = []
        for i in range(len(maxima) - 1):
            first_top = maxima[i]
            second_top = maxima[i + 1]

            # 检查两个顶点间距
            if second_top - first_top < window_size:
                continue

            # 检查两个顶点高度相似
            price_diff = abs(prices[first_top] - prices[second_top]) / prices[first_top]
            if price_diff > similarity_threshold:
                continue

            # 寻找两顶之间的低点
            neck_candidates = [idx for idx in minima if first_top < idx < second_top]
            if not neck_candidates:
                continue

            neck = neck_candidates[np.argmin([prices[idx] for idx in neck_candidates])]

            # 检查颈线与顶点高度差
            height_diff = (prices[first_top] - prices[neck]) / prices[neck]
            if height_diff < height_threshold:
                continue

            # 检查第二个顶点后是否有低于颈线的点 (形成突破)
            breakthrough = False
            for j in range(second_top + 1, min(second_top + window_size, len(prices))):
                if j < len(prices) and prices[j] < prices[neck]:
                    breakthrough = True
                    break

            if breakthrough:
                double_tops.append((first_top, second_top, neck))

        # 双底形态检测
        double_bottoms = []
        for i in range(len(minima) - 1):
            first_bottom = minima[i]
            second_bottom = minima[i + 1]

            # 检查两个底点间距
            if second_bottom - first_bottom < window_size:
                continue

            # 检查两个底点高度相似
            price_diff = (
                abs(prices[first_bottom] - prices[second_bottom]) / prices[first_bottom]
            )
            if price_diff > similarity_threshold:
                continue

            # 寻找两底之间的高点
            neck_candidates = [
                idx for idx in maxima if first_bottom < idx < second_bottom
            ]
            if not neck_candidates:
                continue

            neck = neck_candidates[np.argmax([prices[idx] for idx in neck_candidates])]

            # 检查颈线与底点高度差
            height_diff = (prices[neck] - prices[first_bottom]) / prices[first_bottom]
            if height_diff < height_threshold:
                continue

            # 检查第二个底点后是否有高于颈线的点 (形成突破)
            breakthrough = False
            for j in range(
                second_bottom + 1, min(second_bottom + window_size, len(prices))
            ):
                if j < len(prices) and prices[j] > prices[neck]:
                    breakthrough = True
                    break

            if breakthrough:
                double_bottoms.append((first_bottom, second_bottom, neck))

        return double_tops, double_bottoms


class StockEchartVisualizer:
    """使用Pyecharts绘制基金图表的类"""

    def __init__(self, config=None):
        """初始化StockEchartVisualizer

        Args:
            config: 配置信息字典,可包含主题、大小等设置
        """
        self.data_manager = StockDataManager()
        self.theme = "white"  # Pyecharts主题
        self.width = 1200
        self.height = 800
        self.js_renderer = "canvas"  # 渲染模式:canvas 或 svg

        if config:
            self.theme = config.get("theme", self.theme)
            self.width = config.get("width", self.width)
            self.height = config.get("height", self.height)
            self.js_renderer = config.get("renderer", self.js_renderer)

    def set_theme(self, theme):
        """设置图表主题

        Args:
            theme: Pyecharts支持的主题
        """
        self.theme = theme

    def set_size(self, width, height):
        """设置图表大小

        Args:
            width: 图表宽度
            height: 图表高度
        """
        self.width = width
        self.height = height

    def plot_stock_chart(
        self,
        ts_code,
        display_points=250,
        force_refresh=False,
        long_term=26,
        short_term=13,
        smooth_window=None,
        start_date=None,
        end_date=None,
        add_trendlines=True,
        extrema_window=20,
        min_trend_length=60,
        show_breakthrough=True,
        add_fibonacci=True,
        detect_patterns=True,
    ):
        """
        使用Pyecharts绘制基金图表,展示技术指标
        
        Args:
            ts_code: 基金代码
            display_points: 显示的数据点数量
            force_refresh: 是否强制刷新数据
            long_term: 长期EMA周期
            short_term: 短期EMA周期
            smooth_window: 平滑窗口大小
            start_date: 开始日期
            end_date: 结束日期
            add_trendlines: 是否添加趋势线
            extrema_window: 极值点检测窗口大小
            min_trend_length: 最小趋势长度
            show_breakthrough: 是否显示突破点
            add_fibonacci: 是否添加斐波那契回调线
            detect_patterns: 是否检测形态
        
        Returns:
            Grid: Pyecharts图表对象或None(数据为空时)
        """
        # 1. 获取并预处理数据
        df_full = self.data_manager.get_fund_data(
            ts_code, start_date, end_date, force_refresh
        )

        # 如果数据为空,返回None
        if df_full.empty:
            print(f"警告: 基金 {ts_code} 数据为空.")
            return None

        # 检查数据长度是否足够
        data_length = len(df_full)
        if data_length < max(long_term, short_term) + 10:
            print(f"警告: {ts_code} 数据长度({data_length})不足以可靠计算技术指标")

        # 2. 计算所有技术指标并添加到DataFrame
        df_full['ema_long'] = EMA(df_full['close'], long_term)
        df_full['ema_short'] = EMA(df_full['close'], short_term)
        df_full['ema_5'] = EMA(df_full['close'], 5)
        
        # 计算EMA差值和动量
        df_full['ema_diff'] = df_full['ema_short'] - df_full['ema_long']
        df_full['ema_diff_smoothed'] = TechnicalIndicator.smooth_data(pd.Series(df_full['ema_diff']).fillna(0), window_size=smooth_window)
        
        momentum_window = min(15, data_length // 8)
        if momentum_window % 2 == 0: momentum_window += 1
        
        df_full['momentum'] = TechnicalIndicator.smooth_data(pd.Series(df_full['ema_diff']).diff().fillna(0), window_size=momentum_window)

        # 3. 截取用于显示的数据
        if display_points > data_length:
            print(f"调整显示点数: {display_points} -> {data_length}")
            display_points = data_length
        
        start_idx = max(0, data_length - display_points)
        df = df_full.iloc[start_idx:].reset_index(drop=True)

        # 准备图表基础数据
        x_data = df['trade_date'].tolist()
        price_data = df['close'].tolist()

        # 4. 创建价格主图表
        price_chart = (
            Line()
            .add_xaxis(x_data)
            .add_yaxis(
                "价格",
                price_data,
                is_smooth=True,
                is_symbol_show=False,
                linestyle_opts=opts.LineStyleOpts(width=1, opacity=0.6),
                label_opts=opts.LabelOpts(is_show=False),
            )
            .set_global_opts(
                title_opts=opts.TitleOpts(
                    title="趋势分析",
                    subtitle=f"{ts_code}",
                    pos_left="center",
                    pos_top="0px",
                ),
                xaxis_opts=opts.AxisOpts(
                    type_="category",
                    axislabel_opts=opts.LabelOpts(is_show=False),
                    is_scale=True,
                ),
                yaxis_opts=opts.AxisOpts(
                    name="价格",
                    is_scale=True,
                    axistick_opts=opts.AxisTickOpts(is_show=True),
                    splitline_opts=opts.SplitLineOpts(is_show=True),
                    min_="dataMin",
                ),
                tooltip_opts=opts.TooltipOpts(trigger="axis", axis_pointer_type="cross"),
                legend_opts=opts.LegendOpts(
                    pos_left="5%", pos_top="5%", background_color="rgba(255, 255, 255, 0.6)"
                ),
            )
        )
        
        # 添加EMA线 (Pyecharts会自动处理NaN值,不绘制)
        price_chart.add_yaxis(
            f"{long_term}日EMA", df['ema_long'].tolist(), is_symbol_show=False, label_opts=opts.LabelOpts(is_show=False)
        )
        price_chart.add_yaxis(
            f"{short_term}日EMA", df['ema_short'].tolist(), is_symbol_show=False, label_opts=opts.LabelOpts(is_show=False)
        )
        price_chart.add_yaxis(
            "5日EMA", df['ema_5'].tolist(), is_symbol_show=False, label_opts=opts.LabelOpts(is_show=False),
            linestyle_opts=opts.LineStyleOpts(color="#FF9900")
        )

        # 5. 添加各种叠加图层 (趋势线, 形态, 信号点等)
        # 5.1 添加趋势线
        if add_trendlines:
            # 寻找极值点
            window = min(extrema_window, len(price_data) // 4)
            if window < 3: window = 3
            
            max_indices = argrelextrema(np.array(price_data), np.greater, order=window)[0]
            min_indices = argrelextrema(np.array(price_data), np.less, order=window)[0]

            trendlines_up = []
            valid_lows = sorted(min_indices)
            for i in range(len(valid_lows) - 1):
                start_rel, end_rel = valid_lows[i], valid_lows[i + 1]
                if (end_rel - start_rel) >= min_trend_length and price_data[end_rel] > price_data[start_rel]:
                    trendlines_up.append((start_rel, end_rel))

            trendlines_down = []
            valid_highs = sorted(max_indices)
            for i in range(len(valid_highs) - 1):
                start_rel, end_rel = valid_highs[i], valid_highs[i + 1]
                if (end_rel - start_rel) >= min_trend_length and price_data[end_rel] < price_data[start_rel]:
                    trendlines_down.append((start_rel, end_rel))

            # 绘制趋势线
            for start_rel, end_rel in trendlines_up + trendlines_down:
                is_uptrend = (start_rel, end_rel) in trendlines_up
                color = "green" if is_uptrend else "red"
                
                trend_x = [x_data[start_rel], x_data[end_rel]]
                trend_y = [price_data[start_rel], price_data[end_rel]]
                
                price_chart.overlap(
                    Line().add_xaxis(trend_x).add_yaxis(
                        "", trend_y, is_symbol_show=False, label_opts=opts.LabelOpts(is_show=False),
                        linestyle_opts=opts.LineStyleOpts(width=2, color=color, type_="dashed")
                    )
                )

                # [FIXED] 增加除零保护
                if end_rel > start_rel:
                    slope = (trend_y[1] - trend_y[0]) / (end_rel - start_rel)
                    if end_rel < len(x_data) - 1:
                        extended_x = [x_data[end_rel], x_data[-1]]
                        extended_y_end = [trend_y[1], trend_y[1] + slope * (len(x_data) - 1 - end_rel)]
                        price_chart.overlap(
                            Line().add_xaxis(extended_x).add_yaxis(
                                "", extended_y_end, is_symbol_show=False, label_opts=opts.LabelOpts(is_show=False),
                                linestyle_opts=opts.LineStyleOpts(width=1, color=color, type_="dotted", opacity=0.5)
                            )
                        )
            
            # 5.2 添加突破点
            if show_breakthrough:
                breakthrough_points, success_rate = detect_breakthrough(
                    df_full.iloc[start_idx:], trendlines_up, trendlines_down, price_data
                )
                if breakthrough_points:
                    break_x = [x_data[idx] for idx in breakthrough_points if idx < len(x_data)]
                    break_y = [price_data[idx] for idx in breakthrough_points if idx < len(price_data)]
                    if break_x:
                        price_chart.overlap(
                            Scatter().add_xaxis(break_x).add_yaxis(
                                f"突破点(成功率{success_rate:.0%})", break_y, symbol_size=15, symbol="diamond",
                                itemstyle_opts=opts.ItemStyleOpts(color="gold", border_color="black", border_width=1)
                            )
                        )

        # 5.3 添加斐波那契回调线
        if add_fibonacci and len(price_data) > 0:
            high_price, low_price = max(price_data), min(price_data)
            fib_levels = TechnicalIndicator.calculate_fibonacci_levels(high_price, low_price)
            for level_name, level_value in fib_levels.items():
                if "回调" in level_name:
                    price_chart.overlap(
                        Line().add_xaxis([x_data[0], x_data[-1]]).add_yaxis(
                            f"斐波那契{level_name}", [level_value, level_value], is_symbol_show=False,
                            label_opts=opts.LabelOpts(is_show=False),
                            linestyle_opts=opts.LineStyleOpts(width=1.5, type_="dashed", color="#6A5ACD", opacity=0.7)
                        )
                    )

        # 5.4 添加形态识别
        if detect_patterns and len(price_data) >= 60:
            hs_tops, hs_bottoms = TechnicalIndicator.detect_head_shoulders(price_data, window_size=int(len(price_data) * 0.1))
            double_tops, double_bottoms = TechnicalIndicator.detect_double_top_bottom(price_data, window_size=int(len(price_data) * 0.1))
            
            # 绘制形态 (简化版,可自行扩展)
            for pattern_name, patterns, color in [("头肩顶", hs_tops, "#FF4500"), ("头肩底", hs_bottoms, "#4169E1")]:
                for p in patterns:
                    x = [x_data[p[0]], x_data[p[1]], x_data[p[2]]]
                    y = [price_data[p[0]], price_data[p[1]], price_data[p[2]]]
                    price_chart.overlap(Line().add_xaxis(x).add_yaxis(pattern_name, y, linestyle_opts=opts.LineStyleOpts(color=color)))
            # ... 可继续添加双顶/双底的绘制逻辑

        # 5.5 添加买卖信号点
        crossover = (df['momentum'].shift(1) <= 0) & (df['momentum'] > 0)
        crossunder = (df['momentum'].shift(1) >= 0) & (df['momentum'] < 0)

        buy_points = df[crossover]
        sell_points = df[crossunder]

        if not buy_points.empty:
            price_chart.overlap(
                Scatter().add_xaxis(buy_points['trade_date'].tolist()).add_yaxis(
                    "买入信号", buy_points['close'].tolist(), symbol_size=15, symbol="triangle",
                    itemstyle_opts=opts.ItemStyleOpts(color="green"),
                    label_opts=opts.LabelOpts(is_show=False)  # 设置为不显示标签
                )
            )
        if not sell_points.empty:
             price_chart.overlap(
                Scatter().add_xaxis(sell_points['trade_date'].tolist()).add_yaxis(
                    "卖出信号", sell_points['close'].tolist(), symbol_size=15, symbol="triangle-down",
                    itemstyle_opts=opts.ItemStyleOpts(color="red"),
                    label_opts=opts.LabelOpts(is_show=False)  # 设置为不显示标签
                )
            )

        # 6. 创建附图 (动量图和趋势差异图)
        # 6.1 动量图
        momentum_data = df['momentum'].tolist()
        delta_max = df['momentum'].abs().max()
        scale_factor = min(300 / delta_max, 15) if delta_max > 0 else 10
        scaled_momentum = [v * scale_factor for v in momentum_data]
        
        momentum_chart = (
            Line().add_xaxis(x_data)
            .add_yaxis("动量指标", scaled_momentum, is_smooth=True, is_symbol_show=False,
                       linestyle_opts=opts.LineStyleOpts(width=1.5, color="blue"))
            .set_global_opts(
                xaxis_opts=opts.AxisOpts(axislabel_opts=opts.LabelOpts(is_show=False)),
                yaxis_opts=opts.AxisOpts(name="动量", is_scale=True),
                tooltip_opts=opts.TooltipOpts(trigger="axis"),
                legend_opts=opts.LegendOpts(pos_left="5%", pos_top="36%"),
            )
            .set_series_opts(markline_opts=opts.MarkLineOpts(data=[opts.MarkLineItem(y=0)]))
        )

        # 6.2 趋势差异图
        trend_chart = (
            Line().add_xaxis(x_data)
            .add_yaxis("长期趋势值", (df['ema_diff'] * 2).tolist(), is_smooth=True, is_symbol_show=False,
                       linestyle_opts=opts.LineStyleOpts(width=1, opacity=0.5, color="#800080"))
            .add_yaxis("EWMA平滑", (df['ema_diff_smoothed'] * 2).tolist(), is_smooth=True, is_symbol_show=False,
                       linestyle_opts=opts.LineStyleOpts(width=2, color="#1E90FF"))
            .set_global_opts(
                xaxis_opts=opts.AxisOpts(type_="category", axislabel_opts=opts.LabelOpts(rotate=45)),
                yaxis_opts=opts.AxisOpts(name="趋势差异", is_scale=True),
                tooltip_opts=opts.TooltipOpts(trigger="axis"),
                legend_opts=opts.LegendOpts(pos_left="5%", pos_top="66%"),
            )
            .set_series_opts(markline_opts=opts.MarkLineOpts(data=[opts.MarkLineItem(y=0)]))
        )

        # 7. 组合图表
        grid = Grid(
            init_opts=opts.InitOpts(
                width=f"{self.width}px", height=f"{self.height}px", theme=self.theme,
                renderer=self.js_renderer, page_title="趋势分析"
            )
        )
        grid.add(price_chart, grid_opts=opts.GridOpts(pos_top="15%", pos_bottom="55%"))
        grid.add(momentum_chart, grid_opts=opts.GridOpts(pos_top="50%", pos_bottom="30%"))
        grid.add(trend_chart, grid_opts=opts.GridOpts(pos_top="75%", pos_bottom="5%"))

        return grid


def detect_breakthrough(
    df,
    trendlines_up,
    trendlines_down,
    price_data,
    tolerance=0.01
):
    """
    检测价格对趋势线的突破点
    
    Args:
        df: 数据框
        trendlines_up: 上升趋势线列表
        trendlines_down: 下降趋势线列表
        price_data: 价格数据
        tolerance: 突破容差
        
    Returns:
        tuple: (突破点列表, 成功率)
    """
    breakthrough_points = []
    success_count = 0
    total_count = 0

    if "volume" in df.columns:
        volume_data = df["volume"].values
        volume_ma10 = df["volume"].rolling(window=10).mean().values
    else:
        volume_data = None
        volume_ma10 = None

    # 检查上升趋势线突破
    for start_rel, end_rel in trendlines_up:
        # 增加除零保护
        if end_rel <= start_rel:
            continue
        slope = (price_data[end_rel] - price_data[start_rel]) / (end_rel - start_rel)

        for i in range(end_rel + 1, min(end_rel + 15, len(price_data))):
            expected_value = price_data[start_rel] + slope * (i - start_rel)
            actual_value = price_data[i]

            if actual_value < expected_value * (1 - tolerance):
                total_count += 1
                breakthrough_points.append(i)
                break

    # 检查下降趋势线突破
    for start_rel, end_rel in trendlines_down:
        # 增加除零保护
        if end_rel <= start_rel:
            continue
        slope = (price_data[end_rel] - price_data[start_rel]) / (end_rel - start_rel)

        for i in range(end_rel + 1, min(end_rel + 15, len(price_data))):
            expected_value = price_data[start_rel] + slope * (i - start_rel)
            actual_value = price_data[i]

            if actual_value > expected_value * (1 + tolerance):
                total_count += 1
                breakthrough_points.append(i)
                break

    success_rate = success_count / total_count if total_count > 0 else 0
    return breakthrough_points, success_rate





